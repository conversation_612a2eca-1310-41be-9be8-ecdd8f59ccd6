import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';
import { PendingApprovalElement } from './pending-approval-elements';

export enum enrollmentStatus {
  IN_PROGRESS = 'กำลังเรียน',
  PENDING_APPROVAL = 'รออนุมัติ',
  PENDING_RESULT = 'รอประเมินผลการเรียน',
  PASSED = 'เรียนจบแล้ว',
  COMPLETED = 'สำเร็จ',
  APPROVED = 'อนุมัติ',
  EXPIRED = 'หมดเวลาเรียน',
  REJECTED = 'ไม่อนุมัติ',
  CANCELED = 'ยกเลิกการลงทะเบียน',
  VERIFIED = 'ผ่านการตรวจสอบ',
}

export enum autoApproveReason {
  verifyBySystem = 'ตรวจสอบโดยระบบ',
  approveBySystem = 'ตรวจสอบการอนุมัติโดยระบบ',
  passCheckCitizenId = 'ข้อมูลเลขบัตรประชาชนที่อ่านได้จากบัตรประชาชนกับที่ลงทะเบียนไว้มีความตรงกัน',
  notPassCheckCitizenId = 'ข้อมูลเลขบัตรประชาชนที่อ่านได้จากบัตรประชาชนกับระบบมีความตรงกันต่ำกว่า',
  passCheckFullname = 'ข้อมูลชื่อและนามสกุลที่อ่านได้จากบัตรประชาชนกับที่ลงทะเบียนไว้มีความตรงกัน',
  notPassCheckFullname = 'ข้อมูลชื่อและนามสกุลที่อ่านได้จากบัตรประชาชนกับระบบมีความตรงกันต่ำกว่า',
  captureCitizenIdCardViaWebcam = 'รูปภาพบัตรประชาชนถ่ายผ่านกล้อง/เว็บแคม',
  uploadCitizenIdCard = 'รูปภาพบัตรประชาชนไม่ได้ถ่ายผ่านกล้อง/เว็บแคม ซึ่งไม่ผ่านตามเงื่อนไขที่กำหนด',
  passFaceLiveness = 'ผ่านการตรวจสอบการปลอมแปลงรูปภาพถ่ายใบหน้า',
  notPassFaceLiveness = 'ไม่ผ่านการตรวจสอบการปลอมแปลงรูปภาพถ่ายใบหน้า',
  approvedAllDoc = 'ผ่านการส่งเอกสารทั้งหมด',
  notApproveDoc = 'พบรายการเอกสารที่ยังไม่ได้รับการอนุมัติ',
}

export class PendingApprovalDetailPage extends BasePage {
  readonly editPendingApprovalDetailButtonLocator: Locator = this.page
    .getByRole('button', { name: 'ดูรายละเอียด' })
    .nth(0);
  pendingApprovalElement: PendingApprovalElement;
  readonly successEditEnrollmentApprovalToastMsgLocator: Locator = this.page
    .locator('div')
    .filter({ hasText: 'แก้ไขสถานะการเรียนสำเร็จ' })
    .nth(4);
  readonly enrollmentDetailTab: Locator = this.page.getByRole('tab', { name: 'รายละเอียด' });
  readonly approvalHistoryTab: Locator = this.page.getByRole('tab', { name: 'ประวัติการอนุมัติ' });
  readonly approvalHistoryHeading: Locator = this.page.getByRole('heading', { name: 'ประวัติการอนุมัติ' });
  readonly latestApprovalHistoryTableRow: Locator = this.page
    .locator('tbody.ant-table-tbody tr:not(.ant-table-measure-row)')
    .first();
  readonly latestApprovalHistoryStatusLocator: Locator = this.latestApprovalHistoryTableRow.locator('td:nth-child(2)');
  readonly latestApprovalHistoryReasonLocator: Locator = this.latestApprovalHistoryTableRow.locator('td:last-child');
  readonly approvalHistoryListLocator: Locator = this.page.locator('tr.ant-table-row.ant-table-row-level-0');
  readonly approvalHistoryStatusLocator: Locator = this.approvalHistoryListLocator.locator('td:nth-child(2)');
  readonly approvalHistoryReasonLocator: Locator = this.approvalHistoryListLocator.locator('td:last-child');
  readonly approvalHistoryActionByLocator: Locator = this.approvalHistoryListLocator.locator('td:nth-child(3)');
  readonly enrollmentStatusOnFooterLocator: Locator = this.page
    .locator('footer.ant-layout-footer >> div.ant-space-item:right-of(span:has-text("สถานะ:"))')
    .first();
  readonly verifyCertificateButtonLocator: Locator = this.page.getByRole('link', { name: 'ตรวจสอบประกาศนียบัตร' });
  readonly moreOptionButtonLocator: Locator = this.page
    .locator('footer.ant-layout-footer >> div.ant-space-item:left-of(span:has-text("ตรวจสอบประกาศนีย"))')
    .first();
  readonly resendCertificateButtonLocator: Locator = this.page.getByRole('link', {
    name: 'ออกใบประกาศนียบัตรและส่งอีเมล',
  });

  constructor(page: Page) {
    super(page, '/admin/oicEnrollments');
    this.pendingApprovalElement = new PendingApprovalElement(page, page.locator('.ant-drawer-content'));
  }

  async accessPendingApprovalDetail(): Promise<this> {
    await this.editPendingApprovalDetailButtonLocator.click();

    return this;
  }

  async clickApprovalHistoryTab(): Promise<this> {
    await this.approvalHistoryTab.isVisible();
    await this.approvalHistoryTab.click();

    return this;
  }

  async verifyEnrollmentStatus(status: string): Promise<this> {
    await expect(this.enrollmentStatusOnFooterLocator).toBeVisible();
    switch (status) {
      case 'pending_approval': {
        await expect(this.enrollmentStatusOnFooterLocator).toContainText('รออนุมัติ');
        break;
      }
      case 'verified': {
        await expect(this.enrollmentStatusOnFooterLocator).toContainText('ผ่านการตรวจสอบ');
        break;
      }
      case 'approved': {
        await expect(this.enrollmentStatusOnFooterLocator).toContainText('อนุมัติ');
        break;
      }
      case 'rejected': {
        await expect(this.enrollmentStatusOnFooterLocator).toContainText('ไม่อนุมัติ');
        break;
      }
      default: {
        /* empty */
      }
    }

    return this;
  }

  async verifyAutoApproveApprovalHistory(status: string, failedReasons?: string[]): Promise<this> {
    await expect(this.approvalHistoryHeading).toBeVisible();
    switch (status) {
      case 'notPassAutoVerify': {
        await expect(this.approvalHistoryStatusLocator.first()).toHaveText('กลับไปตรวจสอบ');
        await expect(this.approvalHistoryActionByLocator.first()).toContainText('System');
        await expect(this.approvalHistoryReasonLocator.first()).toContainText(autoApproveReason.verifyBySystem);
        for (const reason of failedReasons) {
          await expect(this.approvalHistoryReasonLocator.first()).toContainText(reason);
        }
        break;
      }
      case 'notPassAutoApprove': {
        await expect(this.approvalHistoryStatusLocator.first()).toHaveText('กลับไปตรวจสอบ');
        await expect(this.approvalHistoryActionByLocator.first()).toContainText('System');
        await expect(this.approvalHistoryReasonLocator.first()).toContainText(autoApproveReason.approveBySystem);
        for (const reason of failedReasons) {
          await expect(this.approvalHistoryReasonLocator.first()).toContainText(reason);
        }
        break;
      }
      case 'passAutoVerify': {
        await expect(this.approvalHistoryStatusLocator.last()).toHaveText('ผ่านการตรวจสอบ');
        await expect(this.approvalHistoryActionByLocator.last()).toContainText('System');
        await expect(this.approvalHistoryReasonLocator.last()).toContainText(autoApproveReason.verifyBySystem);
        await expect(this.approvalHistoryReasonLocator.last()).toContainText(autoApproveReason.passCheckCitizenId);
        await expect(this.approvalHistoryReasonLocator.last()).toContainText(
          autoApproveReason.captureCitizenIdCardViaWebcam,
        );
        break;
      }
      case 'passAutoApprove': {
        await expect(this.approvalHistoryStatusLocator.first()).toHaveText('อนุมัติ');
        await expect(this.approvalHistoryActionByLocator.first()).toContainText('System');
        await expect(this.approvalHistoryReasonLocator.first()).toContainText(autoApproveReason.approveBySystem);
        await expect(this.approvalHistoryReasonLocator.first()).toContainText(autoApproveReason.passCheckCitizenId);
        await expect(this.approvalHistoryReasonLocator.first()).toContainText(
          autoApproveReason.captureCitizenIdCardViaWebcam,
        );
        await expect(this.approvalHistoryReasonLocator.first()).toContainText(autoApproveReason.passFaceLiveness);
        await expect(this.approvalHistoryReasonLocator.first()).toContainText(autoApproveReason.approvedAllDoc);
        break;
      }
      default: {
        /* empty */
      }
    }

    return this;
  }
}
