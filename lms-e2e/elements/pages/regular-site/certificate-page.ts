import { Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';

export class CertificatePage extends BasePage {
  constructor(page: Page) {
    super(page, '/');
  }

  async verifyCertificate(salute: string, firstname: string, lastname: string): Promise<this> {
    await expect(this.page.getByText(`${salute}${firstname} ${lastname}`)).toBeVisible();

    return this;
  }

  async verifyCourseName(courseName: string): Promise<this> {
    await expect(this.page.getByText(courseName)).toBeVisible();

    return this;
  }

  async verifyCertificateContent(
    salute: string,
    firstname: string,
    lastname: string,
    courseName: string,
  ): Promise<this> {
    await this.verifyCertificate(salute, firstname, lastname);
    await this.verifyCourseName(courseName);

    return this;
  }
}
